#pragma once
#include "MainWindow.g.h"
#include "YouTubeService.h"
#include <winrt/Windows.Media.Playback.h>
#include <winrt/Windows.Media.Core.h>
#include <winrt/Windows.Foundation.Collections.h>
#include <winrt/Windows.UI.h>

namespace winrt::Lightning_Shuffler::implementation
{
    struct MainWindow : MainWindowT<MainWindow>
    {
        MainWindow();

        bool IsPlaying() const;
        void IsPlaying(bool value);
        int32_t LoopCount() const;
        void LoopCount(int32_t value);
        hstring CurrentVideoTitle();
        hstring SearchQuery();
        void SearchQuery(hstring const &value);
        Windows::Foundation::Collections::IObservableVector<Windows::Foundation::IInspectable> FilteredVideos();
        Windows::UI::Color VideoAverageColor() const;

        void PlayPauseCommand(Windows::Foundation::IInspectable const &sender, Microsoft::UI::Xaml::RoutedEventArgs const &args);
        void NextVideoCommand(Windows::Foundation::IInspectable const &sender, Microsoft::UI::Xaml::RoutedEventArgs const &args);
        void PreviousVideoCommand(Windows::Foundation::IInspectable const &sender, Microsoft::UI::Xaml::RoutedEventArgs const &args);
        void ShuffleCommand(Windows::Foundation::IInspectable const &sender, Microsoft::UI::Xaml::RoutedEventArgs const &args);
        void ToggleLoopCommand(Windows::Foundation::IInspectable const &sender, Microsoft::UI::Xaml::RoutedEventArgs const &args);
        void AddPlaylistCommand(hstring const &url);
        void OnVideoItemClick(Windows::Foundation::IInspectable const &sender, Microsoft::UI::Xaml::Controls::ItemClickEventArgs const &e);

    private:
        bool m_isPlaying{false};
        int32_t m_loopCount{0};
        hstring m_currentVideoTitle;
        hstring m_searchQuery;
        Windows::UI::Color m_videoAverageColor{Windows::UI::Colors::Transparent()};

        // Media player
        Windows::Media::Playback::MediaPlayer m_mediaPlayer{nullptr};

        // YouTube service
        YouTubeService m_youtubeService;
        std::vector<winrt::Lightning_Shuffler::PlaylistItem> m_playlists;
        std::vector<Video> m_currentQueue;
        std::vector<Video> m_filteredVideos;
        Windows::Foundation::Collections::IObservableVector<Windows::Foundation::IInspectable> m_filteredVideosObservable{nullptr};
        size_t m_currentVideoIndex{0};

        void InitializeMediaPlayer();
        void UpdateMediaControls();
        void UpdatePlayPauseIcon();
        void UpdateLoopCountDisplay();
        Windows::Foundation::IAsyncAction LoadPlaylistAsync(hstring const &url);
        void UpdateQueue();
        void UpdateFilteredVideos();
        void PlayVideo(const Video &video);
        Windows::Foundation::IAsyncAction UpdateVideoColorAsync(const Video &video);
        void StartShuffleAnimation();

        // Internal helper methods (without event parameters)
        void PlayPauseInternal();
        void NextVideoInternal();
        void PreviousVideoInternal();
        void ShuffleInternal();
        void ToggleLoopInternal();
    };
}

namespace winrt::Lightning_Shuffler::factory_implementation
{
    struct MainWindow : MainWindowT<MainWindow, implementation::MainWindow>
    {
    };
}